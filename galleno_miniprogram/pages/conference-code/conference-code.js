// conference-code.js
const auth = require('../../utils/auth.js')
const api = require('../../utils/api.js')

Page({
  data: {
    conferenceId: '',
    conferenceTitle: '2024年度科技创新大会',
    conferenceLocation: '北京国际会议中心',
    conferenceDate: '2024年3月15-17日',
    inputCode: '',
    inputFocused: false,
    shouldFocus: false,
    hasError: false,
    errorMessage: '',
    canVerify: false,
    isVerifying: false,
    remainingAttempts: 3
  },

  // 获取全局数据
  getCorrectCodes: function() {
    const app = getApp()
    return app.globalData.correctCodes || {
      'tech2024': 'TECH2024',
      'ai2024': 'AI2024',
      'blockchain2024': 'BLOCK2024'
    }
  },

  onLoad: function (options) {
    console.log('识别码页面接收到的参数:', options);

    // 获取传递的会议信息
    const conferenceData = {};

    if (options.conferenceId) {
      conferenceData.conferenceId = options.conferenceId;
    }

    if (options.title) {
      conferenceData.conferenceTitle = decodeURIComponent(options.title);
    }

    if (options.location) {
      conferenceData.conferenceLocation = decodeURIComponent(options.location);
    }

    if (options.date) {
      conferenceData.conferenceDate = decodeURIComponent(options.date);
    }

    console.log('设置会议数据:', conferenceData);

    // 设置会议信息
    this.setData(conferenceData);

    // 验证数据设置是否成功
    setTimeout(() => {
      console.log('当前页面数据状态:', {
        conferenceId: this.data.conferenceId,
        conferenceTitle: this.data.conferenceTitle,
        conferenceLocation: this.data.conferenceLocation,
        conferenceDate: this.data.conferenceDate
      });
    }, 100);

    // 验证必要参数
    if (!options.conferenceId) {
      console.error('缺少会议ID参数');
      wx.showToast({
        title: '参数错误，请重新选择会议',
        icon: 'none'
      });
      return;
    }

    // 如果没有传递完整信息，则使用静态数据作为后备
    if (!options.title || !options.location || !options.date) {
      console.log('参数不完整，使用静态数据作为后备');
      this.setConferenceInfo(options.conferenceId);
    }
  },

  onShow: function () {
    // 页面显示时不再自动聚焦输入框
    console.log('会议识别码页面显示');
  },

  // 设置会议信息（静态数据后备方案）
  // 当从列表页面传递的参数不完整时使用
  setConferenceInfo: function (conferenceId) {
    console.log('使用静态数据设置会议信息，conferenceId:', conferenceId);
    const conferenceInfo = {
      'tech2024': {
        title: '2024年度科技创新大会',
        location: '北京国际会议中心',
        date: '2024年3月15-17日'
      },
      'ai2024': {
        title: '人工智能发展论坛',
        location: '上海国际展览中心',
        date: '2024年3月10-12日'
      },
      'blockchain2024': {
        title: '区块链技术峰会',
        location: '深圳会展中心',
        date: '2024年3月20-22日'
      }
    };

    const info = conferenceInfo[conferenceId];
    if (info) {
      this.setData({
        conferenceTitle: info.title,
        conferenceLocation: info.location,
        conferenceDate: info.date
      });
    }
  },

  // 输入框内容变化
  onInputChange: function (e) {
    const rawValue = e.detail.value || '';
    console.log('输入变化:', rawValue); // 调试信息

    // 只在验证时转换大小写，输入时保持原样
    const canVerify = rawValue.length >= 6 && rawValue.length <= 12;

    this.setData({
      inputCode: rawValue,
      canVerify: canVerify,
      hasError: false,
      errorMessage: ''
    });
  },

  // 输入框获得焦点
  onInputFocus: function () {
    this.setData({
      inputFocused: true,
      hasError: false,
      errorMessage: ''
    });
    // 延迟重置focus状态，避免影响输入
    setTimeout(() => {
      this.setData({
        shouldFocus: false
      });
    }, 100);
  },

  // 输入框失去焦点
  onInputBlur: function () {
    this.setData({
      inputFocused: false
    });
    // 延迟重置focus状态
    setTimeout(() => {
      this.setData({
        shouldFocus: false
      });
    }, 100);
  },

  // 验证识别码
  verifyCode: function () {
    if (this.data.isVerifying || this.data.remainingAttempts <= 0) {
      return;
    }

    const code = this.data.inputCode.trim();

    if (code.length < 6) {
      this.showError('识别码长度不能少于6位');
      return;
    }

    if (!this.data.conferenceId) {
      this.showError('会议信息错误，请重新选择会议');
      return;
    }

    // 开始验证
    this.setData({
      isVerifying: true,
      hasError: false,
      errorMessage: ''
    });

    console.log('开始验证识别码:', {
      categoryId: code,
      conferenceId: this.data.conferenceId
    });

    // 先确保用户已登录，然后调用API验证识别码
    auth.ensureLogin({
      showPrompt: true
    }).then(() => {
      console.log('用户已登录，开始调用验证API');

      // 调用后端API验证识别码
      return api.API.conference.validateCode(code, this.data.conferenceId);
    }).then(categoryCode => {
      console.log('识别码验证成功:', categoryCode);
      // 验证成功，传递categoryCode数据
      this.verifySuccess(categoryCode);
    }).catch(err => {
      console.error('验证过程失败:', err);

      // 区分不同类型的错误
      if (err.message && err.message.includes('登录')) {
        this.verifyFailed('请先登录后再验证识别码');
      } else if (err.message && err.message.includes('网络')) {
        this.verifyFailed('网络连接失败，请检查网络后重试');
      } else {
        this.verifyFailed(err.message || '识别码验证失败');
      }
    });
  },

  // 验证成功
  verifySuccess: function (categoryCode) {
    this.setData({
      isVerifying: false
    });

    wx.showToast({
      title: '验证成功',
      icon: 'success',
      duration: 1000
    });

    // 保存categoryCode数据，用于后续页面
    const categoryCodeData = categoryCode || {};
    console.log('保存categoryCode数据:', categoryCodeData);

    setTimeout(() => {
      // 检查登录状态，如果未登录则引导登录
      auth.ensureLogin({
        showPrompt: true
      }).then(() => {
        // 已登录，跳转到酒店预订页面，传递更多参数
        const params = [
          `conferenceId=${this.data.conferenceId}`,
          `title=${encodeURIComponent(this.data.conferenceTitle)}`,
          `location=${encodeURIComponent(this.data.conferenceLocation)}`,
          `date=${encodeURIComponent(this.data.conferenceDate)}`,
          `categoryId=${encodeURIComponent(this.data.inputCode.trim())}`,
          `categoryCodeId=${categoryCodeData.id || ''}`
        ].join('&');

        wx.navigateTo({
          url: `/pages/hotel-booking/hotel-booking?${params}`
        });
      }).catch(err => {
        console.log('用户取消登录或登录失败:', err);
        // 可以选择跳转到登录页面或停留在当前页面
      });
    }, 1000);
  },

  // 验证失败
  verifyFailed: function (errorMessage) {
    // 确保重置验证状态
    this.setData({
      isVerifying: false
    });

    // 如果是网络错误或登录错误，不减少尝试次数
    const isNetworkOrLoginError = errorMessage && (
      errorMessage.includes('网络') ||
      errorMessage.includes('登录') ||
      errorMessage.includes('连接')
    );

    let remainingAttempts = this.data.remainingAttempts;
    if (!isNetworkOrLoginError) {
      remainingAttempts = remainingAttempts - 1;
      this.setData({
        remainingAttempts: remainingAttempts
      });
    }

    // 使用传入的错误信息，如果没有则使用默认信息
    let message = errorMessage || '识别码错误';

    if (remainingAttempts > 0) {
      if (isNetworkOrLoginError) {
        this.showError(message);
      } else {
        this.showError(`${message}，还有${remainingAttempts}次机会`);
      }
    } else {
      this.showError('验证次数已用完，请联系客服');
      this.setData({
        canVerify: false
      });
    }
  },

  // 显示错误信息
  showError: function (message) {
    this.setData({
      hasError: true,
      errorMessage: message
    });

    // 震动反馈
    wx.vibrateShort();

    // 错误后不再自动聚焦输入框，让用户手动控制
  },

  // 手动触发输入框聚焦
  focusInput: function() {
    console.log('手动触发聚焦'); // 调试信息
    this.setData({
      shouldFocus: true
    });
    // 确保focus状态能够被重置
    setTimeout(() => {
      this.setData({
        shouldFocus: false
      });
    }, 200);
  },

  // 重置验证状态（用于调试或重试）
  resetVerifyState: function() {
    this.setData({
      isVerifying: false,
      hasError: false,
      errorMessage: '',
      remainingAttempts: 3,
      canVerify: true
    });
    console.log('验证状态已重置');
  },

  // 联系客服
  contactService: function () {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：************\n工作时间：9:00-18:00',
      showCancel: true,
      cancelText: '取消',
      confirmText: '拨打',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '4008889999',
            fail: () => {
              wx.showToast({
                title: '拨打失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 返回上一页
  onNavigateBack: function () {
    wx.navigateBack();
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: `${this.data.conferenceTitle} - 会议酒店预定`,
      path: `/pages/conference-code/conference-code?conferenceId=${this.data.conferenceId}&title=${encodeURIComponent(this.data.conferenceTitle)}`
    };
  }
});
