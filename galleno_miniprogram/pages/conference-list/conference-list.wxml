<!--conference-list.wxml-->
<view class="container flex flex-column">
  <!-- 顶部横幅 -->
  <view class="banner">
    <view class="banner-content">
      <view class="banner-text">
        <text class="banner-title">会议酒店预定平台</text>
        <text class="banner-subtitle">为您提供专业的会议住宿服务</text>
      </view>
      <view class="banner-icon">
        <text class="iconfont">📅</text>
      </view>
    </view>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-section">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-list">
        <view class="filter-item {{currentFilter === 'all' ? 'active' : ''}}" 
              bindtap="filterConferences" data-type="all">
          全部
        </view>
        <view class="filter-item {{currentFilter === 'bookable' ? 'active' : ''}}" 
              bindtap="filterConferences" data-type="bookable">
          可预订
        </view>
        <view class="filter-item {{currentFilter === 'ongoing' ? 'active' : ''}}" 
              bindtap="filterConferences" data-type="ongoing">
          进行中
        </view>
        <view class="filter-item {{currentFilter === 'ended' ? 'active' : ''}}" 
              bindtap="filterConferences" data-type="ended">
          已结束
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 会议列表 -->
  <scroll-view class="conference-list" scroll-y="true" enhanced="true" show-scrollbar="false"
               refresher-enabled="true" refresher-triggered="{{loading}}" bindrefresherrefresh="onRefresh">
    <view class="conference-item {{item.status}}" 
          wx:for="{{filteredConferences}}" 
          wx:key="id"
          bindtap="goToConference" 
          data-conference="{{item}}">
      
      <!-- 会议图片 -->
      <view class="conference-image-container">
        <image class="conference-image" src="{{item.image}}" mode="aspectFill" lazy-load="true" binderror="onImageError" bindload="onImageLoad"></image>
        
        <!-- 状态标签 -->
        <view class="status-tag {{item.status}}">
          <text class="status-icon">{{item.statusIcon}}</text>
          <text class="status-text">{{item.statusText}}</text>
        </view>
        
        <!-- 收藏按钮 -->
        <view class="favorite-btn" bindtap="toggleFavorite" data-id="{{item.id}}" catchtap="true">
          <text class="favorite-icon {{item.isFavorite ? 'active' : ''}}">♥</text>
        </view>
      </view>

      <!-- 会议信息 -->
      <view class="conference-info {{item.status === 'ended' ? 'ended' : ''}}">
        <text class="conference-title">{{item.title}}</text>
        
        <view class="conference-detail">
          <text class="detail-icon">📅</text>
          <text class="detail-text">{{item.date}}</text>
        </view>
        
        <view class="conference-detail">
          <text class="detail-icon">📍</text>
          <text class="detail-text">{{item.location}}</text>
        </view>
        
        <view class="conference-stats">
          <view class="stat-item">
            <text class="stat-icon">👥</text>
            <text class="stat-text">{{item.participants}}+{{isSmallScreen ? '人' : '参会人员'}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-text {{item.roomStatus === 'available' ? 'available' : item.roomStatus === 'full' ? 'full' : 'pending'}}">
              {{item.roomStatusText}}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <text class="loading-icon">⏳</text>
      <text class="loading-text">正在加载会议列表...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && filteredConferences.length === 0}}">
      <text class="empty-icon">📋</text>
      <text class="empty-text">暂无相关会议</text>
      <text class="empty-subtitle">请尝试其他筛选条件</text>
    </view>

    <!-- 底部间距 -->
    <view class="bottom-spacing"></view>
  </scroll-view>
</view>
