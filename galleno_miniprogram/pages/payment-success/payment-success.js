// payment-success.js
Page({
  data: {
    orderData: {}
  },

  onLoad: function (options) {
    // 获取订单数据
    const orderData = wx.getStorageSync('currentOrder');
    if (orderData) {
      this.setData({
        orderData: orderData
      });
    } else {
      // 如果没有订单数据，跳转到首页
      wx.showModal({
        title: '提示',
        content: '订单信息丢失',
        showCancel: false,
        success: () => {
          this.backToHome();
        }
      });
    }
  },

  onShow: function () {
    // 禁用返回按钮，防止用户返回到支付页面
    wx.hideHomeButton();
  },

  // 查看订单详情
  viewOrderDetail: function () {
    // 在实际开发中，这里应该跳转到订单详情页面
    wx.showModal({
      title: '订单详情',
      content: `订单号：${this.data.orderData.orderId}\n状态：已支付\n支付时间：${this.formatTime(this.data.orderData.paymentTime)}`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 返回首页
  backToHome: function () {
    wx.switchTab({
      url: '/pages/conference-list/conference-list'
    });
  },

  // 格式化时间
  formatTime: function (timeString) {
    if (!timeString) return '';
    
    const date = new Date(timeString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: '我在会议酒店预定平台成功预订了房间',
      path: '/pages/conference-list/conference-list',
      imageUrl: '/images/share-success.png' // 可以添加分享图片
    };
  },

  // 禁用返回按钮
  onNavigateBack: function () {
    wx.showModal({
      title: '提示',
      content: '支付已完成，是否返回首页？',
      success: (res) => {
        if (res.confirm) {
          this.backToHome();
        }
      }
    });
    return false; // 阻止默认返回行为
  }
});
