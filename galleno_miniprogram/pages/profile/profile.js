// profile.js
const auth = require('../../utils/auth.js')
const api = require('../../utils/api.js')

Page({
  data: {
    isLoggedIn: false,
    isLogging: false, // 登录中状态
    userInfo: {
      nickName: '',
      avatarUrl: '',
      memberLevel: '普通会员',
      openId: '',
      unionId: '',
      isNewUser: false
    },
    bookingStats: {
      pending: 0,
      paid: 0,
      checkedin: 0,
      completed: 0
    },
    recentBookings: []
  },

  onLoad: function (options) {
    this.checkLoginStatus();
  },

  onShow: function () {
    this.checkLoginStatus();
    if (this.data.isLoggedIn) {
      this.loadBookingData();
    }
  },

  // 检查登录状态
  checkLoginStatus: function () {
    const isLoggedIn = auth.isLoggedIn();
    const userInfo = auth.getCurrentUser();

    this.setData({
      isLoggedIn: isLoggedIn,
      userInfo: userInfo.nickName ? userInfo : {
        nickName: '',
        avatarUrl: '',
        memberLevel: '普通会员',
        openId: '',
        unionId: '',
        isNewUser: false
      }
    });

    // 如果已登录，加载预订数据
    if (isLoggedIn) {
      this.loadBookingData();
    }
  },

  // 微信登录
  login: function () {
    if (this.data.isLogging) {
      return; // 防止重复点击
    }

    this.setData({
      isLogging: true
    });

    auth.login({
      success: (result) => {
        console.log('登录成功:', result);

        this.setData({
          isLoggedIn: true,
          userInfo: auth.getCurrentUser(),
          isLogging: false
        });

        // 加载预订数据
        this.loadBookingData();
      },
      fail: (err) => {
        console.error('登录失败:', err);
        this.setData({
          isLogging: false
        });
      }
    });
  },

  // 退出登录
  logout: function () {
    auth.logout({
      success: () => {
        this.setData({
          isLoggedIn: false,
          isLogging: false,
          userInfo: {
            nickName: '',
            avatarUrl: '',
            memberLevel: '普通会员',
            openId: '',
            unionId: '',
            isNewUser: false
          },
          bookingStats: {
            pending: 0,
            paid: 0,
            checkedin: 0,
            completed: 0
          },
          recentBookings: []
        });
      }
    });
  },

  // 加载预订数据
  loadBookingData: function () {
    console.log('开始加载预订数据...');

    // 调用接口获取订单列表
    api.API.order.getList()
      .then(orders => {
        console.log('获取订单列表成功:', orders);
        this.processOrderData(orders);
      })
      .catch(err => {
        console.error('获取订单列表失败:', err);
        // 如果接口失败，尝试从本地存储获取数据作为后备方案
        this.loadLocalBookingData();
      });
  },

  // 处理订单数据
  processOrderData: function (orders) {
    const orderList = Array.isArray(orders) ? orders : (orders.data || []);

    // 统计各状态订单数量
    const stats = {
      pending: 0,
      paid: 0,
      checkedin: 0,
      completed: 0
    };

    const processedBookings = orderList.map(order => {
      // 统计状态
      if (order.status === 'pending') stats.pending++;
      else if (order.status === 'paid') stats.paid++;
      else if (order.status === 'checkedin') stats.checkedin++;
      else if (order.status === 'completed') stats.completed++;

      // 处理订单数据用于显示
      return {
        orderId: order.orderId || order.id,
        roomName: order.roomName || order.roomTitle || '未知房型',
        checkinDate: this.formatDate(order.checkinDate || order.checkInDate),
        checkoutDate: this.formatDate(order.checkoutDate || order.checkOutDate),
        nights: this.calculateNights(order.checkinDate || order.checkInDate, order.checkoutDate || order.checkOutDate),
        totalPrice: order.totalPrice || order.totalAmount || 0,
        depositAmount: order.depositAmount || order.deposit || 0,
        status: order.status,
        statusText: this.getStatusText(order.status),
        conferenceTitle: order.conferenceTitle || order.conferenceName || '',
        createTime: order.createTime || order.createdAt
      };
    });

    // 按创建时间排序，最新的在前
    processedBookings.sort((a, b) => {
      const timeA = new Date(a.createTime || 0).getTime();
      const timeB = new Date(b.createTime || 0).getTime();
      return timeB - timeA;
    });

    this.setData({
      bookingStats: stats,
      recentBookings: processedBookings.slice(0, 3) // 只显示最近3个订单
    });
  },

  // 从本地存储加载数据（后备方案）
  loadLocalBookingData: function () {
    console.log('使用本地数据作为后备方案');
    const orderHistory = wx.getStorageSync('orderHistory') || [];

    const stats = {
      pending: 0,
      paid: 0,
      checkedin: 0,
      completed: 0
    };

    const processedBookings = orderHistory.map(order => {
      if (order.status === 'pending') stats.pending++;
      else if (order.status === 'paid') stats.paid++;
      else if (order.status === 'checkedin') stats.checkedin++;
      else if (order.status === 'completed') stats.completed++;

      return {
        ...order,
        statusText: this.getStatusText(order.status)
      };
    });

    this.setData({
      bookingStats: stats,
      recentBookings: processedBookings.slice(0, 3)
    });
  },

  // 获取状态文本
  getStatusText: function (status) {
    const statusMap = {
      'pending': '待支付',
      'paid': '已支付',
      'checkedin': '已入住',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || '未知状态';
  },

  // 格式化日期
  formatDate: function (dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr;

    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${month}-${day}`;
  },

  // 计算住宿天数
  calculateNights: function (checkinDate, checkoutDate) {
    if (!checkinDate || !checkoutDate) return 1;

    const checkin = new Date(checkinDate);
    const checkout = new Date(checkoutDate);

    if (isNaN(checkin.getTime()) || isNaN(checkout.getTime())) return 1;

    const diffTime = checkout.getTime() - checkin.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(1, diffDays);
  },

  // 筛选预订
  filterBookings: function (e) {
    const status = e.currentTarget.dataset.status;
    // 在实际开发中，这里应该跳转到订单列表页面并筛选对应状态
    wx.showToast({
      title: `查看${this.getStatusText(status)}订单`,
      icon: 'none'
    });
  },

  // 查看所有预订
  viewAllBookings: function () {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    // 跳转到订单列表页面
    wx.showToast({
      title: '跳转到订单列表',
      icon: 'none'
    });
  },

  // 查看预订详情
  viewBookingDetail: function (e) {
    const order = e.currentTarget.dataset.order;
    wx.showModal({
      title: '订单详情',
      content: `订单号：${order.orderId}\n房型：${order.roomName}\n状态：${order.statusText}\n总价：¥${order.totalPrice}`,
      showCancel: false
    });
  },





  // 显示帮助
  showHelp: function () {
    wx.showModal({
      title: '使用帮助',
      content: '1. 选择会议并输入识别码\n2. 选择房型和入住日期\n3. 填写入住人信息\n4. 支付定金完成预订\n5. 凭订单入住酒店',
      showCancel: false
    });
  },

  // 显示反馈
  showFeedback: function () {
    wx.showModal({
      title: '意见反馈',
      content: '感谢您的使用！如有意见或建议，请联系客服：400-888-9999',
      showCancel: false
    });
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: '会议酒店预定平台',
      path: '/pages/conference-list/conference-list'
    };
  }
});
