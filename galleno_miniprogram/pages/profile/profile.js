// profile.js
const auth = require('../../utils/auth.js')

Page({
  data: {
    isLoggedIn: false,
    isLogging: false, // 登录中状态
    userInfo: {
      nickName: '',
      avatarUrl: '',
      memberLevel: '普通会员',
      openId: '',
      unionId: '',
      isNewUser: false
    },
    bookingStats: {
      pending: 0,
      paid: 0,
      checkedin: 0,
      completed: 0
    },
    recentBookings: []
  },

  onLoad: function (options) {
    this.checkLoginStatus();
  },

  onShow: function () {
    this.checkLoginStatus();
    if (this.data.isLoggedIn) {
      this.loadBookingData();
    }
  },

  // 检查登录状态
  checkLoginStatus: function () {
    const isLoggedIn = auth.isLoggedIn();
    const userInfo = auth.getCurrentUser();

    this.setData({
      isLoggedIn: isLoggedIn,
      userInfo: userInfo.nickName ? userInfo : {
        nickName: '',
        avatarUrl: '',
        memberLevel: '普通会员',
        openId: '',
        unionId: '',
        isNewUser: false
      }
    });

    // 如果已登录，加载预订数据
    if (isLoggedIn) {
      this.loadBookingData();
    }
  },

  // 微信登录
  login: function () {
    if (this.data.isLogging) {
      return; // 防止重复点击
    }

    this.setData({
      isLogging: true
    });

    auth.login({
      success: (result) => {
        console.log('登录成功:', result);

        this.setData({
          isLoggedIn: true,
          userInfo: auth.getCurrentUser(),
          isLogging: false
        });

        // 加载预订数据
        this.loadBookingData();
      },
      fail: (err) => {
        console.error('登录失败:', err);
        this.setData({
          isLogging: false
        });
      }
    });
  },

  // 退出登录
  logout: function () {
    auth.logout({
      success: () => {
        this.setData({
          isLoggedIn: false,
          isLogging: false,
          userInfo: {
            nickName: '',
            avatarUrl: '',
            memberLevel: '普通会员',
            openId: '',
            unionId: '',
            isNewUser: false
          },
          bookingStats: {
            pending: 0,
            paid: 0,
            checkedin: 0,
            completed: 0
          },
          recentBookings: []
        });
      }
    });
  },

  // 加载预订数据
  loadBookingData: function () {
    // 从本地存储获取订单历史
    const orderHistory = wx.getStorageSync('orderHistory') || [];
    
    // 统计各状态订单数量
    const stats = {
      pending: 0,
      paid: 0,
      checkedin: 0,
      completed: 0
    };

    const processedBookings = orderHistory.map(order => {
      // 统计状态
      if (order.status === 'pending') stats.pending++;
      else if (order.status === 'paid') stats.paid++;
      else if (order.status === 'checkedin') stats.checkedin++;
      else if (order.status === 'completed') stats.completed++;

      // 处理订单数据用于显示
      return {
        ...order,
        statusText: this.getStatusText(order.status),
        roomImage: this.getRoomImage(order.roomType)
      };
    });

    this.setData({
      bookingStats: stats,
      recentBookings: processedBookings.slice(0, 3) // 只显示最近3个订单
    });
  },

  // 获取状态文本
  getStatusText: function (status) {
    const statusMap = {
      'pending': '待支付',
      'paid': '已支付',
      'checkedin': '已入住',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || '未知状态';
  },

  // 获取房型图片
  getRoomImage: function (roomType) {
    const imageMap = {
      'deluxe': 'https://images.unsplash.com/photo-1611892440504-42a792e24d32?w=60&h=45&fit=crop',
      'business': 'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=60&h=45&fit=crop',
      'suite': 'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=60&h=45&fit=crop',
      'standard': 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=60&h=45&fit=crop'
    };
    return imageMap[roomType] || imageMap['standard'];
  },

  // 筛选预订
  filterBookings: function (e) {
    const status = e.currentTarget.dataset.status;
    // 在实际开发中，这里应该跳转到订单列表页面并筛选对应状态
    wx.showToast({
      title: `查看${this.getStatusText(status)}订单`,
      icon: 'none'
    });
  },

  // 查看所有预订
  viewAllBookings: function () {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    // 跳转到订单列表页面
    wx.showToast({
      title: '跳转到订单列表',
      icon: 'none'
    });
  },

  // 查看预订详情
  viewBookingDetail: function (e) {
    const order = e.currentTarget.dataset.order;
    wx.showModal({
      title: '订单详情',
      content: `订单号：${order.orderId}\n房型：${order.roomName}\n状态：${order.statusText}\n总价：¥${order.totalPrice}`,
      showCancel: false
    });
  },

  // 跳转到订单页面
  goToBookings: function () {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    wx.showToast({
      title: '跳转到订单列表',
      icon: 'none'
    });
  },

  // 跳转到收藏页面
  goToFavorites: function () {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    wx.showToast({
      title: '跳转到收藏列表',
      icon: 'none'
    });
  },

  // 跳转到设置页面
  goToSettings: function () {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    wx.showToast({
      title: '跳转到设置页面',
      icon: 'none'
    });
  },

  // 联系客服
  contactService: function () {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：************\n工作时间：9:00-18:00',
      showCancel: true,
      cancelText: '取消',
      confirmText: '拨打',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '4008889999',
            fail: () => {
              wx.showToast({
                title: '拨打失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 显示帮助
  showHelp: function () {
    wx.showModal({
      title: '使用帮助',
      content: '1. 选择会议并输入识别码\n2. 选择房型和入住日期\n3. 填写入住人信息\n4. 支付定金完成预订\n5. 凭订单入住酒店',
      showCancel: false
    });
  },

  // 显示反馈
  showFeedback: function () {
    wx.showModal({
      title: '意见反馈',
      content: '感谢您的使用！如有意见或建议，请联系客服：************',
      showCancel: false
    });
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: '会议酒店预定平台',
      path: '/pages/conference-list/conference-list'
    };
  }
});
