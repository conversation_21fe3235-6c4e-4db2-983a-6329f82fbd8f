// profile.js
const auth = require('../../utils/auth.js')
const api = require('../../utils/api.js')

Page({
  data: {
    isLoggedIn: false,
    isLogging: false, // 登录中状态
    userInfo: {
      nickName: '',
      avatarUrl: '',
      memberLevel: '普通会员',
      openId: '',
      unionId: '',
      isNewUser: false
    },
    recentBookings: []
  },

  onLoad: function (options) {
    this.checkLoginStatus();
  },

  onShow: function () {
    this.checkLoginStatus();
    if (this.data.isLoggedIn) {
      this.loadBookingData();
    }
  },

  // 检查登录状态
  checkLoginStatus: function () {
    const isLoggedIn = auth.isLoggedIn();
    const userInfo = auth.getCurrentUser();

    this.setData({
      isLoggedIn: isLoggedIn,
      userInfo: userInfo.nickName ? userInfo : {
        nickName: '',
        avatarUrl: '',
        memberLevel: '普通会员',
        openId: '',
        unionId: '',
        isNewUser: false
      }
    });

    // 如果已登录，加载预订数据
    if (isLoggedIn) {
      this.loadBookingData();
    }
  },

  // 微信登录
  login: function () {
    if (this.data.isLogging) {
      return; // 防止重复点击
    }

    this.setData({
      isLogging: true
    });

    auth.login({
      success: (result) => {
        console.log('登录成功:', result);

        this.setData({
          isLoggedIn: true,
          userInfo: auth.getCurrentUser(),
          isLogging: false
        });

        // 加载预订数据
        this.loadBookingData();
      },
      fail: (err) => {
        console.error('登录失败:', err);
        this.setData({
          isLogging: false
        });
      }
    });
  },

  // 退出登录
  logout: function () {
    auth.logout({
      success: () => {
        this.setData({
          isLoggedIn: false,
          isLogging: false,
          userInfo: {
            nickName: '',
            avatarUrl: '',
            memberLevel: '普通会员',
            openId: '',
            unionId: '',
            isNewUser: false
          },
          bookingStats: {
            pending: 0,
            paid: 0,
            checkedin: 0,
            completed: 0
          },
          recentBookings: []
        });
      }
    });
  },

  // 加载预订数据
  loadBookingData: function () {
    console.log('开始加载预订数据...');

    // 调用接口获取订单列表
    api.API.order.getList()
      .then(orders => {
        console.log('获取订单列表成功:', orders);
        this.processOrderData(orders);
      })
      .catch(err => {
        console.error('获取订单列表失败:', err);
        // 如果接口失败，尝试从本地存储获取数据作为后备方案
        this.loadLocalBookingData();
      });
  },

  // 处理订单数据
  processOrderData: function (orders) {
    const orderList = Array.isArray(orders) ? orders : (orders.data || []);
    console.log('处理订单数据:', orderList);

    const processedBookings = orderList.map(order => {
      console.log('处理单个订单:', order);
      console.log('订单状态:', order.orderStatus || order.status, '类型:', typeof (order.orderStatus || order.status));

      // 使用orderStatus字段，如果没有则使用status字段
      const statusValue = order.orderStatus || order.status;

      // 标准化状态值
      const normalizedStatus = this.normalizeStatus(statusValue);
      console.log('标准化后状态:', normalizedStatus);

      // 处理订单数据用于显示
      return {
        orderId: order.orderId || order.id,
        orderNo: order.orderNo || order.orderNumber,
        roomName: order.roomName || order.roomTitle || '未知房型',
        checkinDate: this.formatDate(order.checkinDate || order.checkInDate),
        checkoutDate: this.formatDate(order.checkoutDate || order.checkOutDate),
        nights: order.nights || this.calculateNights(order.checkinDate || order.checkInDate, order.checkoutDate || order.checkOutDate),
        totalPrice: order.totalAmount || order.totalPrice || 0,
        depositAmount: order.depositAmount || order.deposit || 0,
        status: normalizedStatus,
        statusText: this.getStatusText(normalizedStatus),
        conferenceTitle: order.conferenceTitle || order.conferenceName || '',
        createTime: order.createTime || order.createdAt,
        refundAmount: order.refundAmount || 0,
        refundTime: order.refundTime
      };
    });

    // 按创建时间排序，最新的在前
    processedBookings.sort((a, b) => {
      const timeA = new Date(a.createTime || 0).getTime();
      const timeB = new Date(b.createTime || 0).getTime();
      return timeB - timeA;
    });

    this.setData({
      recentBookings: processedBookings.slice(0, 3) // 只显示最近3个订单
    });
  },

  // 从本地存储加载数据（后备方案）
  loadLocalBookingData: function () {
    console.log('使用本地数据作为后备方案');
    const orderHistory = wx.getStorageSync('orderHistory') || [];

    const processedBookings = orderHistory.map(order => {
      return {
        ...order,
        statusText: this.getStatusText(order.status)
      };
    });

    this.setData({
      recentBookings: processedBookings.slice(0, 3)
    });
  },

  // 标准化状态值
  normalizeStatus: function (status) {
    if (!status) return 'pending';

    const statusStr = String(status).toLowerCase();

    // 状态映射表 - 将各种可能的后端状态值映射到标准状态
    const statusMapping = {
      // 待支付相关
      'pending': 'pending',
      'unpaid': 'pending',
      'wait_pay': 'pending',
      'waitpay': 'pending',
      'created': 'pending',
      '0': 'pending',

      // 已支付相关
      'paid': 'paid',
      'payed': 'paid',
      'success': 'paid',
      'confirmed': 'paid',
      'payment_success': 'paid',
      '1': 'paid',

      // 已入住相关
      'checkedin': 'checkedin',
      'checked_in': 'checkedin',
      'in_use': 'checkedin',
      'using': 'checkedin',
      '2': 'checkedin',

      // 已完成相关
      'completed': 'completed',
      'finished': 'completed',
      'done': 'completed',
      'checkout': 'completed',
      'checked_out': 'completed',
      '3': 'completed',

      // 已取消相关
      'cancelled': 'cancelled',
      'canceled': 'cancelled',
      'cancel': 'cancelled',
      '4': 'cancelled',
      '-1': 'cancelled',

      // 已退款相关
      'refunded': 'refunded',
      'refund': 'refunded',
      'refund_success': 'refunded',
      '5': 'refunded'
    };

    return statusMapping[statusStr] || 'pending';
  },

  // 获取状态文本
  getStatusText: function (status) {
    const statusMap = {
      'pending': '待支付',
      'paid': '已支付',
      'checkedin': '已入住',
      'completed': '已完成',
      'cancelled': '已取消',
      'refunded': '已退款'
    };
    return statusMap[status] || `未知状态(${status})`;
  },

  // 格式化日期
  formatDate: function (dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr;

    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${month}-${day}`;
  },

  // 计算住宿天数
  calculateNights: function (checkinDate, checkoutDate) {
    if (!checkinDate || !checkoutDate) return 1;

    const checkin = new Date(checkinDate);
    const checkout = new Date(checkoutDate);

    if (isNaN(checkin.getTime()) || isNaN(checkout.getTime())) return 1;

    const diffTime = checkout.getTime() - checkin.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(1, diffDays);
  },



  // 查看所有预订
  viewAllBookings: function () {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    // 跳转到订单列表页面
    wx.showToast({
      title: '跳转到订单列表',
      icon: 'none'
    });
  },







  // 显示帮助
  showHelp: function () {
    wx.showModal({
      title: '使用帮助',
      content: '1. 选择会议并输入识别码\n2. 选择房型和入住日期\n3. 填写入住人信息\n4. 支付定金完成预订\n5. 凭订单入住酒店',
      showCancel: false
    });
  },

  // 显示反馈
  showFeedback: function () {
    wx.showModal({
      title: '意见反馈',
      content: '感谢您的使用！如有意见或建议，请联系客服：400-888-9999',
      showCancel: false
    });
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: '会议酒店预定平台',
      path: '/pages/conference-list/conference-list'
    };
  }
});
