/* profile.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 用户信息区域 */
.user-section {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  margin: 32rpx;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  color: #ffffff;
}

/* 未登录状态 */
.login-prompt {
  text-align: center;
}

.login-avatar {
  width: 160rpx;
  height: 160rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 32rpx;
}

.avatar-icon {
  font-size: 80rpx;
}

.login-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.login-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 32rpx;
}

.login-btn {
  background: #ffffff;
  color: #3b82f6;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.login-btn.loading {
  background: #f3f4f6;
  color: #9ca3af;
}

.login-btn::after {
  border: none;
}

/* 已登录状态 */
.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  margin-right: 32rpx;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.user-level {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-btn::after {
  border: none;
}

.logout-icon {
  font-size: 24rpx;
}

/* 我的预订 */
.booking-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.view-all {
  font-size: 26rpx;
  color: #3b82f6;
}



/* 最近预订 */
.recent-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 24rpx;
  padding-top: 32rpx;
  border-top: 2rpx solid #f1f5f9;
}

.booking-item {
  border: 2rpx solid #f1f5f9;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  transition: all 0.3s ease;
}

.booking-item:last-child {
  margin-bottom: 0;
}

.booking-item:active {
  background: #f9fafb;
}

.booking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.booking-number {
  font-size: 26rpx;
  color: #6b7280;
}

.booking-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.booking-status.paid {
  background: #dbeafe;
  color: #3b82f6;
}

.booking-status.pending {
  background: #fef3c7;
  color: #f59e0b;
}

.booking-status.checkedin {
  background: #d1fae5;
  color: #10b981;
}

.booking-status.completed {
  background: #f3f4f6;
  color: #6b7280;
}

.booking-status.cancelled {
  background: #fee2e2;
  color: #ef4444;
}

.booking-status.refunded {
  background: #fef3c7;
  color: #f59e0b;
}

.booking-content {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.booking-info {
  flex: 1;
}

.booking-main-info {
  margin-bottom: 8rpx;
}

.booking-room {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4rpx;
}

.booking-conference {
  display: block;
  font-size: 24rpx;
  color: #3b82f6;
  margin-bottom: 4rpx;
}

.booking-date {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
}

.booking-price {
  text-align: right;
  margin-left: 16rpx;
}

.price-amount {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #f59e0b;
  margin-bottom: 4rpx;
}

.price-deposit {
  display: block;
  font-size: 22rpx;
  color: #6b7280;
}



/* 功能菜单 */
.menu-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f1f5f9;
  transition: all 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #f9fafb;
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  font-size: 32rpx;
}

.menu-icon.orders {
  background: #dbeafe;
}

.menu-icon.favorites {
  background: #fecaca;
}

.menu-icon.settings {
  background: #f3f4f6;
}

.menu-icon.service {
  background: #d1fae5;
}

.menu-content {
  flex: 1;
}

.menu-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4rpx;
}

.menu-subtitle {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
}

.menu-arrow {
  font-size: 32rpx;
  color: #9ca3af;
}

/* 帮助与反馈 */
.help-section {
  margin: 32rpx;
  width: calc(100% - 64rpx);
  box-sizing: border-box;
}

.help-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  width: 100%;
}

.help-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 48rpx 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.help-item:active {
  background: #f9fafb;
}

.help-icon {
  display: block;
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.help-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #1f2937;
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 32rpx;
  color: #9ca3af;
}

.version-text {
  display: block;
  font-size: 24rpx;
  margin-bottom: 8rpx;
}

.copyright {
  display: block;
  font-size: 22rpx;
}

/* 响应式适配 - 使用rpx单位自动适配 */
