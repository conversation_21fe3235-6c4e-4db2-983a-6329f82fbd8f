package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CategoryCodeRoomMapper;
import com.ruoyi.system.domain.CategoryCodeRoom;
import com.ruoyi.system.service.ICategoryCodeRoomService;

/**
 * 识别码房型关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Service
public class CategoryCodeRoomServiceImpl implements ICategoryCodeRoomService 
{
    @Autowired
    private CategoryCodeRoomMapper categoryCodeRoomMapper;

    /**
     * 查询识别码房型关联
     * 
     * @param id 识别码房型关联主键
     * @return 识别码房型关联
     */
    @Override
    public CategoryCodeRoom selectCategoryCodeRoomById(Long id)
    {
        return categoryCodeRoomMapper.selectCategoryCodeRoomById(id);
    }

    /**
     * 查询识别码房型关联列表
     * 
     * @param categoryCodeRoom 识别码房型关联
     * @return 识别码房型关联
     */
    @Override
    public List<CategoryCodeRoom> selectCategoryCodeRoomList(CategoryCodeRoom categoryCodeRoom)
    {
        return categoryCodeRoomMapper.selectCategoryCodeRoomList(categoryCodeRoom);
    }

    /**
     * 根据识别码和会议ID查询房型关联列表
     * 
     * @param categoryId 识别码
     * @param conference 会议ID
     * @return 识别码房型关联集合
     */
    @Override
    public List<CategoryCodeRoom> selectRoomsByCategoryCode(String categoryId, Long conference)
    {
        return categoryCodeRoomMapper.selectRoomsByCategoryCode(categoryId, conference);
    }

    /**
     * 新增识别码房型关联
     * 
     * @param categoryCodeRoom 识别码房型关联
     * @return 结果
     */
    @Override
    public int insertCategoryCodeRoom(CategoryCodeRoom categoryCodeRoom)
    {
        categoryCodeRoom.setCreateTime(DateUtils.getNowDate());
        return categoryCodeRoomMapper.insertCategoryCodeRoom(categoryCodeRoom);
    }

    /**
     * 批量新增识别码房型关联
     * 
     * @param categoryCodeRoomList 识别码房型关联列表
     * @return 结果
     */
    @Override
    public int batchInsertCategoryCodeRoom(List<CategoryCodeRoom> categoryCodeRoomList)
    {
        for (CategoryCodeRoom categoryCodeRoom : categoryCodeRoomList) {
            categoryCodeRoom.setCreateTime(DateUtils.getNowDate());
        }
        return categoryCodeRoomMapper.batchInsertCategoryCodeRoom(categoryCodeRoomList);
    }

    /**
     * 修改识别码房型关联
     * 
     * @param categoryCodeRoom 识别码房型关联
     * @return 结果
     */
    @Override
    public int updateCategoryCodeRoom(CategoryCodeRoom categoryCodeRoom)
    {
        return categoryCodeRoomMapper.updateCategoryCodeRoom(categoryCodeRoom);
    }

    /**
     * 批量删除识别码房型关联
     * 
     * @param ids 需要删除的识别码房型关联主键
     * @return 结果
     */
    @Override
    public int deleteCategoryCodeRoomByIds(Long[] ids)
    {
        return categoryCodeRoomMapper.deleteCategoryCodeRoomByIds(ids);
    }

    /**
     * 删除识别码房型关联信息
     * 
     * @param id 识别码房型关联主键
     * @return 结果
     */
    @Override
    public int deleteCategoryCodeRoomById(Long id)
    {
        return categoryCodeRoomMapper.deleteCategoryCodeRoomById(id);
    }

    /**
     * 根据识别码和会议ID删除房型关联
     * 
     * @param categoryId 识别码
     * @param conference 会议ID
     * @return 结果
     */
    @Override
    public int deleteByCategoryCode(String categoryId, Long conference)
    {
        return categoryCodeRoomMapper.deleteByCategoryCode(categoryId, conference);
    }
}
